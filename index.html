<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Superellipse Tool</title>
    <script src="https://unpkg.com/paper@0.12.17/dist/paper-full.min.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            startup: {
                ready: () => {
                    MathJax.startup.defaultReady();
                    console.log('MathJax is loaded and ready');
                }
            }
        };
    </script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .controls {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
        }

        .controls-left {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: center;
        }

        .controls-right {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .control-group label {
            font-weight: bold;
            font-size: 14px;
            color: #333;
        }
        
        .control-group input[type="range"] {
            width: 150px;
        }
        
        .control-group input[type="number"] {
            width: 80px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .export-btn {
            background: #007bff;
            color: white;
            border: 3px solid #007bff;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
        }

        .export-btn:hover {
            background: #0056b3;
            border-color: #0056b3;
        }

        .load-save-btn {
            background: transparent;
            color: black;
            border: 3px solid black;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
        }

        .load-save-btn:hover {
            background: #f0f0f0;
        }
        
        .canvas-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            position: relative;
        }

        canvas {
            display: block;
            width: 100%;
            height: 600px;
        }

        .value-display {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }

        .formula-overlay {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 15px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            font-size: 16px;
            z-index: 10;
            pointer-events: none;
            font-family: 'Times New Roman', serif;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Superellipse Tool</h1>
        
        <div class="controls">
            <div class="controls-left">
                <div class="control-group">
                    <label for="width">Width</label>
                    <input type="range" id="width" min="50" max="500" value="300" step="10">
                    <div class="value-display" id="width-value">300</div>
                </div>

                <div class="control-group">
                    <label for="height">Height</label>
                    <input type="range" id="height" min="50" max="500" value="220" step="10">
                    <div class="value-display" id="height-value">220</div>
                </div>

                <div class="control-group">
                    <label for="exponent">Exponent (n)</label>
                    <input type="range" id="exponent" min="0.5" max="5" value="2.5" step="0.1">
                    <div class="value-display" id="exponent-value">2.5</div>
                </div>

                <div class="control-group">
                    <label for="smoothing">Smoothing</label>
                    <input type="range" id="smoothing" min="0.3" max="0.8" value="0.55" step="0.01">
                    <div class="value-display" id="smoothing-value">0.55</div>
                </div>
            </div>

            <div class="controls-right">
                <input type="file" id="load-settings" accept=".json,.svg" style="display: none;">
                <button class="load-save-btn" id="load-settings-btn">Load</button>
                <button class="load-save-btn" id="save-settings">Save</button>
                <button class="export-btn" id="export-svg">Export SVG</button>
            </div>
        </div>
        
        <div class="canvas-container">
            <canvas id="superellipse-canvas" resize></canvas>
            <div class="formula-overlay" id="formula-overlay">
                |x/150|<sup>2.5</sup> + |y/110|<sup>2.5</sup> = 1
            </div>
        </div>
    </div>

    <script>
        // Initialize Paper.js
        paper.install(window);
        paper.setup('superellipse-canvas');
        
        let superellipsePath = null;
        
        // Superellipse parameters
        let params = {
            width: 300,
            height: 220,
            exponent: 2.5,
            smoothing: 0.55
        };
        
        // Function to create superellipse using 4 bezier curves (one per quarter)
        function createSuperellipse() {
            if (superellipsePath) {
                superellipsePath.remove();
            }

            const { width, height, exponent, smoothing } = params;
            const a = width / 2;
            const b = height / 2;
            const n = exponent;

            superellipsePath = new Path();
            superellipsePath.strokeColor = 'black';
            superellipsePath.strokeWidth = 2;
            superellipsePath.fillColor = null;

            // Calculate the 4 cardinal points of the superellipse
            // North (top)
            const north = new Point(0, -b);
            // East (right)
            const east = new Point(a, 0);
            // South (bottom)
            const south = new Point(0, b);
            // West (left)
            const west = new Point(-a, 0);

            // Calculate handle lengths based on superellipse curvature and smoothing factor
            // For a superellipse, the optimal handle length is related to the exponent
            // This formula provides good approximation for different exponent values
            const kappa = smoothing * (4/3) * Math.pow(2, 1/n - 1);

            const handleX = kappa * a;
            const handleY = kappa * b;

            // Start at the top (north) point
            superellipsePath.moveTo(north);

            // Quarter 1: North to East (top-right)
            superellipsePath.cubicCurveTo(
                new Point(handleX, -b),     // handle out from north
                new Point(a, -handleY),     // handle in to east
                east                        // east point
            );

            // Quarter 2: East to South (bottom-right)
            superellipsePath.cubicCurveTo(
                new Point(a, handleY),      // handle out from east
                new Point(handleX, b),      // handle in to south
                south                       // south point
            );

            // Quarter 3: South to West (bottom-left)
            superellipsePath.cubicCurveTo(
                new Point(-handleX, b),     // handle out from south
                new Point(-a, handleY),     // handle in to west
                west                        // west point
            );

            // Quarter 4: West to North (top-left)
            superellipsePath.cubicCurveTo(
                new Point(-a, -handleY),    // handle out from west
                new Point(-handleX, -b),    // handle in to north
                north                       // north point (closes the path)
            );

            superellipsePath.closePath();

            // Center the shape
            superellipsePath.position = view.center;

            view.draw();
        }
        
        // Update formula display
        function updateFormula() {
            const { width, height, exponent } = params;
            const a = width / 2;
            const b = height / 2;
            const formulaText = `|x/${a}|<sup>${exponent}</sup> + |y/${b}|<sup>${exponent}</sup> = 1`;
            document.getElementById('formula-overlay').innerHTML = formulaText;
        }

        // Update parameter values and redraw
        function updateParameter(param, value) {
            params[param] = parseFloat(value);
            document.getElementById(param + '-value').textContent = value;
            updateFormula();
            createSuperellipse();
        }

        // Update UI controls to match current parameters
        function updateUI() {
            document.getElementById('width').value = params.width;
            document.getElementById('height').value = params.height;
            document.getElementById('exponent').value = params.exponent;
            document.getElementById('smoothing').value = params.smoothing;

            document.getElementById('width-value').textContent = params.width;
            document.getElementById('height-value').textContent = params.height;
            document.getElementById('exponent-value').textContent = params.exponent;
            document.getElementById('smoothing-value').textContent = params.smoothing;

            updateFormula();
        }

        // Parse settings from filename (for SVG files)
        function parseFilename(filename) {
            // Remove file extension
            const nameWithoutExt = filename.replace(/\.(svg|json)$/i, '');

            // Look for pattern: superellipse-w[width]-h[height]-e[exponent]-s[smoothing]
            const match = nameWithoutExt.match(/superellipse-w(\d+)-h(\d+)-e([\d_]+)-s([\d_]+)/);

            if (match) {
                return {
                    width: parseInt(match[1]),
                    height: parseInt(match[2]),
                    exponent: parseFloat(match[3].replace('_', '.')),
                    smoothing: parseFloat(match[4].replace('_', '.'))
                };
            }
            return null;
        }
        
        // Set up event listeners
        document.getElementById('width').addEventListener('input', (e) => {
            updateParameter('width', e.target.value);
        });
        
        document.getElementById('height').addEventListener('input', (e) => {
            updateParameter('height', e.target.value);
        });
        
        document.getElementById('exponent').addEventListener('input', (e) => {
            updateParameter('exponent', e.target.value);
        });
        
        document.getElementById('smoothing').addEventListener('input', (e) => {
            updateParameter('smoothing', e.target.value);
        });
        
        // Export SVG function
        document.getElementById('export-svg').addEventListener('click', () => {
            if (!superellipsePath) return;

            const svg = project.exportSVG({ asString: true });
            const blob = new Blob([svg], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(blob);

            // Create filename with parameter values
            const { width, height, exponent, smoothing } = params;
            const filename = `superellipse-w${width}-h${height}-e${exponent.toString().replace('.', '_')}-s${smoothing.toString().replace('.', '_')}.svg`;

            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        });

        // Save settings function
        document.getElementById('save-settings').addEventListener('click', () => {
            const settings = {
                version: "1.0",
                timestamp: new Date().toISOString(),
                parameters: { ...params }
            };

            const json = JSON.stringify(settings, null, 2);
            const blob = new Blob([json], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            // Create filename with parameter values (same format as SVG)
            const { width, height, exponent, smoothing } = params;
            const filename = `superellipse-w${width}-h${height}-e${exponent.toString().replace('.', '_')}-s${smoothing.toString().replace('.', '_')}.json`;

            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        });

        // Load settings button
        document.getElementById('load-settings-btn').addEventListener('click', () => {
            document.getElementById('load-settings').click();
        });

        // Load settings function
        document.getElementById('load-settings').addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (!file) return;

            const reader = new FileReader();

            reader.onload = (event) => {
                try {
                    if (file.name.toLowerCase().endsWith('.json')) {
                        // Load from JSON file
                        const settings = JSON.parse(event.target.result);
                        if (settings.parameters) {
                            params = { ...settings.parameters };
                            updateUI();
                            createSuperellipse();
                        } else {
                            alert('Invalid settings file format');
                        }
                    } else if (file.name.toLowerCase().endsWith('.svg')) {
                        // Parse settings from SVG filename
                        const parsedParams = parseFilename(file.name);
                        if (parsedParams) {
                            params = { ...parsedParams };
                            updateUI();
                            createSuperellipse();
                        } else {
                            alert('Could not parse settings from filename. Please use a file exported from this tool.');
                        }
                    }
                } catch (error) {
                    alert('Error loading settings: ' + error.message);
                }
            };

            reader.readAsText(file);

            // Reset the file input so the same file can be loaded again
            e.target.value = '';
        });
        
        // Initial draw
        updateFormula();
        createSuperellipse();
        
        // Handle window resize
        view.onResize = function() {
            if (superellipsePath) {
                superellipsePath.position = view.center;
            }
        };
    </script>
</body>
</html>
